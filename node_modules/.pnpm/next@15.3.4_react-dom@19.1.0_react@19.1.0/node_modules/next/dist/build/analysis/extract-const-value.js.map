{"version": 3, "sources": ["../../../src/build/analysis/extract-const-value.ts"], "sourcesContent": ["import type {\n  ArrayExpression,\n  BooleanLiteral,\n  ExportDeclaration,\n  Identifier,\n  KeyValueProperty,\n  Module,\n  Node,\n  NullLiteral,\n  NumericLiteral,\n  ObjectExpression,\n  RegExpLiteral,\n  StringLiteral,\n  TemplateLiteral,\n  TsSatisfiesExpression,\n  VariableDeclaration,\n} from '@swc/core'\n\nexport class NoSuchDeclarationError extends Error {}\n\nfunction isExportDeclaration(node: Node): node is ExportDeclaration {\n  return node.type === 'ExportDeclaration'\n}\n\nfunction isVariableDeclaration(node: Node): node is VariableDeclaration {\n  return node.type === 'VariableDeclaration'\n}\n\nfunction isIdentifier(node: Node): node is Identifier {\n  return node.type === 'Identifier'\n}\n\nfunction isBooleanLiteral(node: Node): node is BooleanLiteral {\n  return node.type === 'BooleanLiteral'\n}\n\nfunction isNullLiteral(node: Node): node is NullLiteral {\n  return node.type === 'NullLiteral'\n}\n\nfunction isStringLiteral(node: Node): node is StringLiteral {\n  return node.type === 'StringLiteral'\n}\n\nfunction isNumericLiteral(node: Node): node is NumericLiteral {\n  return node.type === 'NumericLiteral'\n}\n\nfunction isArrayExpression(node: Node): node is ArrayExpression {\n  return node.type === 'ArrayExpression'\n}\n\nfunction isObjectExpression(node: Node): node is ObjectExpression {\n  return node.type === 'ObjectExpression'\n}\n\nfunction isKeyValueProperty(node: Node): node is KeyValueProperty {\n  return node.type === 'KeyValueProperty'\n}\n\nfunction isRegExpLiteral(node: Node): node is RegExpLiteral {\n  return node.type === 'RegExpLiteral'\n}\n\nfunction isTemplateLiteral(node: Node): node is TemplateLiteral {\n  return node.type === 'TemplateLiteral'\n}\n\nfunction isTsSatisfiesExpression(node: Node): node is TsSatisfiesExpression {\n  return node.type === 'TsSatisfiesExpression'\n}\n\nexport class UnsupportedValueError extends Error {\n  /** @example `config.runtime[0].value` */\n  path?: string\n\n  constructor(message: string, paths?: string[]) {\n    super(message)\n\n    // Generating \"path\" that looks like \"config.runtime[0].value\"\n    let codePath: string | undefined\n    if (paths) {\n      codePath = ''\n      for (const path of paths) {\n        if (path[0] === '[') {\n          // \"array\" + \"[0]\"\n          codePath += path\n        } else {\n          if (codePath === '') {\n            codePath = path\n          } else {\n            // \"object\" + \".key\"\n            codePath += `.${path}`\n          }\n        }\n      }\n    }\n\n    this.path = codePath\n  }\n}\n\nfunction extractValue(node: Node, path?: string[]): any {\n  if (isNullLiteral(node)) {\n    return null\n  } else if (isBooleanLiteral(node)) {\n    // e.g. true / false\n    return node.value\n  } else if (isStringLiteral(node)) {\n    // e.g. \"abc\"\n    return node.value\n  } else if (isNumericLiteral(node)) {\n    // e.g. 123\n    return node.value\n  } else if (isRegExpLiteral(node)) {\n    // e.g. /abc/i\n    return new RegExp(node.pattern, node.flags)\n  } else if (isIdentifier(node)) {\n    switch (node.value) {\n      case 'undefined':\n        return undefined\n      default:\n        throw new UnsupportedValueError(\n          `Unknown identifier \"${node.value}\"`,\n          path\n        )\n    }\n  } else if (isArrayExpression(node)) {\n    // e.g. [1, 2, 3]\n    const arr = []\n    for (let i = 0, len = node.elements.length; i < len; i++) {\n      const elem = node.elements[i]\n      if (elem) {\n        if (elem.spread) {\n          // e.g. [ ...a ]\n          throw new UnsupportedValueError(\n            'Unsupported spread operator in the Array Expression',\n            path\n          )\n        }\n\n        arr.push(extractValue(elem.expression, path && [...path, `[${i}]`]))\n      } else {\n        // e.g. [1, , 2]\n        //         ^^\n        arr.push(undefined)\n      }\n    }\n    return arr\n  } else if (isObjectExpression(node)) {\n    // e.g. { a: 1, b: 2 }\n    const obj: any = {}\n    for (const prop of node.properties) {\n      if (!isKeyValueProperty(prop)) {\n        // e.g. { ...a }\n        throw new UnsupportedValueError(\n          'Unsupported spread operator in the Object Expression',\n          path\n        )\n      }\n\n      let key\n      if (isIdentifier(prop.key)) {\n        // e.g. { a: 1, b: 2 }\n        key = prop.key.value\n      } else if (isStringLiteral(prop.key)) {\n        // e.g. { \"a\": 1, \"b\": 2 }\n        key = prop.key.value\n      } else {\n        throw new UnsupportedValueError(\n          `Unsupported key type \"${prop.key.type}\" in the Object Expression`,\n          path\n        )\n      }\n\n      obj[key] = extractValue(prop.value, path && [...path, key])\n    }\n\n    return obj\n  } else if (isTemplateLiteral(node)) {\n    // e.g. `abc`\n    if (node.expressions.length !== 0) {\n      // TODO: should we add support for `${'e'}d${'g'}'e'`?\n      throw new UnsupportedValueError(\n        'Unsupported template literal with expressions',\n        path\n      )\n    }\n\n    // When TemplateLiteral has 0 expressions, the length of quasis is always 1.\n    // Because when parsing TemplateLiteral, the parser yields the first quasi,\n    // then the first expression, then the next quasi, then the next expression, etc.,\n    // until the last quasi.\n    // Thus if there is no expression, the parser ends at the frst and also last quasis\n    //\n    // A \"cooked\" interpretation where backslashes have special meaning, while a\n    // \"raw\" interpretation where backslashes do not have special meaning\n    // https://exploringjs.com/impatient-js/ch_template-literals.html#template-strings-cooked-vs-raw\n    const [{ cooked, raw }] = node.quasis\n\n    return cooked ?? raw\n  } else if (isTsSatisfiesExpression(node)) {\n    return extractValue(node.expression)\n  } else {\n    throw new UnsupportedValueError(\n      `Unsupported node type \"${node.type}\"`,\n      path\n    )\n  }\n}\n\n/**\n * Extracts the value of an exported const variable named `exportedName`\n * (e.g. \"export const config = { runtime: 'edge' }\") from swc's AST.\n * The value must be one of (or throws UnsupportedValueError):\n *   - string\n *   - boolean\n *   - number\n *   - null\n *   - undefined\n *   - array containing values listed in this list\n *   - object containing values listed in this list\n *\n * Throws NoSuchDeclarationError if the declaration is not found.\n */\nexport function extractExportedConstValue(\n  module: Module,\n  exportedName: string\n): any {\n  for (const moduleItem of module.body) {\n    if (!isExportDeclaration(moduleItem)) {\n      continue\n    }\n\n    const declaration = moduleItem.declaration\n    if (!isVariableDeclaration(declaration)) {\n      continue\n    }\n\n    if (declaration.kind !== 'const') {\n      continue\n    }\n\n    for (const decl of declaration.declarations) {\n      if (\n        isIdentifier(decl.id) &&\n        decl.id.value === exportedName &&\n        decl.init\n      ) {\n        return extractValue(decl.init, [exportedName])\n      }\n    }\n  }\n\n  throw new NoSuchDeclarationError()\n}\n"], "names": ["NoSuchDeclarationError", "UnsupportedValueError", "extractExportedConstValue", "Error", "isExportDeclaration", "node", "type", "isVariableDeclaration", "isIdentifier", "isBooleanLiteral", "is<PERSON>ull<PERSON>iteral", "isStringLiteral", "isNumericLiteral", "isArrayExpression", "isObjectExpression", "isKeyValueProperty", "isRegExpLiteral", "isTemplateLiteral", "isTsSatisfiesExpression", "constructor", "message", "paths", "codePath", "path", "extractValue", "value", "RegExp", "pattern", "flags", "undefined", "arr", "i", "len", "elements", "length", "elem", "spread", "push", "expression", "obj", "prop", "properties", "key", "expressions", "cooked", "raw", "quasis", "module", "exportedName", "moduleItem", "body", "declaration", "kind", "decl", "declarations", "id", "init"], "mappings": ";;;;;;;;;;;;;;;;IAkBaA,sBAAsB;eAAtBA;;IAsDAC,qBAAqB;eAArBA;;IAyJGC,yBAAyB;eAAzBA;;;AA/MT,MAAMF,+BAA+BG;AAAO;AAEnD,SAASC,oBAAoBC,IAAU;IACrC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASC,sBAAsBF,IAAU;IACvC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASE,aAAaH,IAAU;IAC9B,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASG,iBAAiBJ,IAAU;IAClC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASI,cAAcL,IAAU;IAC/B,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASK,gBAAgBN,IAAU;IACjC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASM,iBAAiBP,IAAU;IAClC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASO,kBAAkBR,IAAU;IACnC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASQ,mBAAmBT,IAAU;IACpC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASS,mBAAmBV,IAAU;IACpC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASU,gBAAgBX,IAAU;IACjC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASW,kBAAkBZ,IAAU;IACnC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEA,SAASY,wBAAwBb,IAAU;IACzC,OAAOA,KAAKC,IAAI,KAAK;AACvB;AAEO,MAAML,8BAA8BE;IAIzCgB,YAAYC,OAAe,EAAEC,KAAgB,CAAE;QAC7C,KAAK,CAACD;QAEN,8DAA8D;QAC9D,IAAIE;QACJ,IAAID,OAAO;YACTC,WAAW;YACX,KAAK,MAAMC,QAAQF,MAAO;gBACxB,IAAIE,IAAI,CAAC,EAAE,KAAK,KAAK;oBACnB,kBAAkB;oBAClBD,YAAYC;gBACd,OAAO;oBACL,IAAID,aAAa,IAAI;wBACnBA,WAAWC;oBACb,OAAO;wBACL,oBAAoB;wBACpBD,YAAY,CAAC,CAAC,EAAEC,MAAM;oBACxB;gBACF;YACF;QACF;QAEA,IAAI,CAACA,IAAI,GAAGD;IACd;AACF;AAEA,SAASE,aAAanB,IAAU,EAAEkB,IAAe;IAC/C,IAAIb,cAAcL,OAAO;QACvB,OAAO;IACT,OAAO,IAAII,iBAAiBJ,OAAO;QACjC,oBAAoB;QACpB,OAAOA,KAAKoB,KAAK;IACnB,OAAO,IAAId,gBAAgBN,OAAO;QAChC,aAAa;QACb,OAAOA,KAAKoB,KAAK;IACnB,OAAO,IAAIb,iBAAiBP,OAAO;QACjC,WAAW;QACX,OAAOA,KAAKoB,KAAK;IACnB,OAAO,IAAIT,gBAAgBX,OAAO;QAChC,cAAc;QACd,OAAO,IAAIqB,OAAOrB,KAAKsB,OAAO,EAAEtB,KAAKuB,KAAK;IAC5C,OAAO,IAAIpB,aAAaH,OAAO;QAC7B,OAAQA,KAAKoB,KAAK;YAChB,KAAK;gBACH,OAAOI;YACT;gBACE,MAAM,IAAI5B,sBACR,CAAC,oBAAoB,EAAEI,KAAKoB,KAAK,CAAC,CAAC,CAAC,EACpCF;QAEN;IACF,OAAO,IAAIV,kBAAkBR,OAAO;QAClC,iBAAiB;QACjB,MAAMyB,MAAM,EAAE;QACd,IAAK,IAAIC,IAAI,GAAGC,MAAM3B,KAAK4B,QAAQ,CAACC,MAAM,EAAEH,IAAIC,KAAKD,IAAK;YACxD,MAAMI,OAAO9B,KAAK4B,QAAQ,CAACF,EAAE;YAC7B,IAAII,MAAM;gBACR,IAAIA,KAAKC,MAAM,EAAE;oBACf,gBAAgB;oBAChB,MAAM,IAAInC,sBACR,uDACAsB;gBAEJ;gBAEAO,IAAIO,IAAI,CAACb,aAAaW,KAAKG,UAAU,EAAEf,QAAQ;uBAAIA;oBAAM,CAAC,CAAC,EAAEQ,EAAE,CAAC,CAAC;iBAAC;YACpE,OAAO;gBACL,gBAAgB;gBAChB,aAAa;gBACbD,IAAIO,IAAI,CAACR;YACX;QACF;QACA,OAAOC;IACT,OAAO,IAAIhB,mBAAmBT,OAAO;QACnC,sBAAsB;QACtB,MAAMkC,MAAW,CAAC;QAClB,KAAK,MAAMC,QAAQnC,KAAKoC,UAAU,CAAE;YAClC,IAAI,CAAC1B,mBAAmByB,OAAO;gBAC7B,gBAAgB;gBAChB,MAAM,IAAIvC,sBACR,wDACAsB;YAEJ;YAEA,IAAImB;YACJ,IAAIlC,aAAagC,KAAKE,GAAG,GAAG;gBAC1B,sBAAsB;gBACtBA,MAAMF,KAAKE,GAAG,CAACjB,KAAK;YACtB,OAAO,IAAId,gBAAgB6B,KAAKE,GAAG,GAAG;gBACpC,0BAA0B;gBAC1BA,MAAMF,KAAKE,GAAG,CAACjB,KAAK;YACtB,OAAO;gBACL,MAAM,IAAIxB,sBACR,CAAC,sBAAsB,EAAEuC,KAAKE,GAAG,CAACpC,IAAI,CAAC,0BAA0B,CAAC,EAClEiB;YAEJ;YAEAgB,GAAG,CAACG,IAAI,GAAGlB,aAAagB,KAAKf,KAAK,EAAEF,QAAQ;mBAAIA;gBAAMmB;aAAI;QAC5D;QAEA,OAAOH;IACT,OAAO,IAAItB,kBAAkBZ,OAAO;QAClC,aAAa;QACb,IAAIA,KAAKsC,WAAW,CAACT,MAAM,KAAK,GAAG;YACjC,sDAAsD;YACtD,MAAM,IAAIjC,sBACR,iDACAsB;QAEJ;QAEA,4EAA4E;QAC5E,2EAA2E;QAC3E,kFAAkF;QAClF,wBAAwB;QACxB,mFAAmF;QACnF,EAAE;QACF,4EAA4E;QAC5E,qEAAqE;QACrE,gGAAgG;QAChG,MAAM,CAAC,EAAEqB,MAAM,EAAEC,GAAG,EAAE,CAAC,GAAGxC,KAAKyC,MAAM;QAErC,OAAOF,UAAUC;IACnB,OAAO,IAAI3B,wBAAwBb,OAAO;QACxC,OAAOmB,aAAanB,KAAKiC,UAAU;IACrC,OAAO;QACL,MAAM,IAAIrC,sBACR,CAAC,uBAAuB,EAAEI,KAAKC,IAAI,CAAC,CAAC,CAAC,EACtCiB;IAEJ;AACF;AAgBO,SAASrB,0BACd6C,OAAc,EACdC,YAAoB;IAEpB,KAAK,MAAMC,cAAcF,QAAOG,IAAI,CAAE;QACpC,IAAI,CAAC9C,oBAAoB6C,aAAa;YACpC;QACF;QAEA,MAAME,cAAcF,WAAWE,WAAW;QAC1C,IAAI,CAAC5C,sBAAsB4C,cAAc;YACvC;QACF;QAEA,IAAIA,YAAYC,IAAI,KAAK,SAAS;YAChC;QACF;QAEA,KAAK,MAAMC,QAAQF,YAAYG,YAAY,CAAE;YAC3C,IACE9C,aAAa6C,KAAKE,EAAE,KACpBF,KAAKE,EAAE,CAAC9B,KAAK,KAAKuB,gBAClBK,KAAKG,IAAI,EACT;gBACA,OAAOhC,aAAa6B,KAAKG,IAAI,EAAE;oBAACR;iBAAa;YAC/C;QACF;IACF;IAEA,MAAM,IAAIhD;AACZ"}