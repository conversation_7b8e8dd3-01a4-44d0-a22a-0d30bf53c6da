{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-error-browser-binary-loader.ts"], "sourcesContent": ["import type { webpack } from 'next/dist/compiled/webpack/webpack'\n\nexport default function nextErrorBrowserBinaryLoader(\n  this: webpack.LoaderContext<any>\n) {\n  const { resourcePath, rootContext } = this\n  const relativePath = resourcePath.slice(rootContext.length + 1)\n  throw new Error(\n    `Node.js binary module ./${relativePath} is not supported in the browser. Please only use the module on server side`\n  )\n}\n"], "names": ["nextErrorBrowserBinaryLoader", "resourcePath", "rootContext", "relativePath", "slice", "length", "Error"], "mappings": ";;;;+BAEA;;;eAAwBA;;;AAAT,SAASA;IAGtB,MAAM,EAAEC,YAAY,EAAEC,WAAW,EAAE,GAAG,IAAI;IAC1C,MAAMC,eAAeF,aAAaG,KAAK,CAACF,YAAYG,MAAM,GAAG;IAC7D,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,wBAAwB,EAAEH,aAAa,2EAA2E,CAAC,GADhH,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF"}