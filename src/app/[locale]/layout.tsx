import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from 'next-intl/server';
import {notFound} from 'next/navigation';
import type {Metadata} from 'next';

const locales = ['en', 'es'];

type Props = {
  children: React.ReactNode;
  params: {locale: string};
};

export async function generateMetadata({params}: Props): Promise<Metadata> {
  const {locale} = params;
  
  // Import messages for SEO
  const messages = await getMessages();
  const seoMessages = messages.seo as any;
  
  return {
    title: seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',
    description: seoMessages?.description || 'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',
    openGraph: {
      title: seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',
      description: seoMessages?.description || 'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',
      url: 'https://ojochal.cr',
      siteName: 'Ojochal.cr',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'Ojochal, Costa Rica - Hidden Gem',
        },
      ],
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',
      description: seoMessages?.description || 'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',
      images: ['/og-image.jpg'],
    },
    icons: {
      icon: '/favicon.ico',
    },
  };
}

export default async function LocaleLayout({children, params}: Props) {
  const {locale} = params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
