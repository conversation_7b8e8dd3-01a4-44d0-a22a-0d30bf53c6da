'use client';

import {motion} from 'framer-motion';
import {useTranslations} from 'next-intl';
import {useEffect, useState} from 'react';
import EmailForm from '@/components/EmailForm';
import LanguageToggle from '@/components/LanguageToggle';

export default function HomePage() {
  const t = useTranslations();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background Image with Animation */}
      <motion.div
        initial={{scale: 1.1}}
        animate={{scale: 1}}
        transition={{duration: 10, ease: 'easeOut'}}
        className="absolute inset-0 z-0"
      >
        <div
          className="w-full h-full bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-800 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), linear-gradient(135deg, #059669 0%, #0f766e 50%, #155e75 100%)`,
          }}
        />
      </motion.div>

      {/* Language Toggle */}
      <LanguageToggle />

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col items-center justify-center px-6 text-center text-white">
        {/* Hero Section */}
        <motion.div
          initial={{opacity: 0, y: 50}}
          animate={{opacity: 1, y: 0}}
          transition={{duration: 0.8, delay: 0.2}}
          className="max-w-4xl mx-auto mb-12"
        >
          {/* Main Heading */}
          <motion.h1
            initial={{opacity: 0, y: 30}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.6, delay: 0.4}}
            className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent"
          >
            {t('hero.title')}
          </motion.h1>

          {/* Discover Ojochal */}
          <motion.h2
            initial={{opacity: 0, y: 30}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.6, delay: 0.5}}
            className="text-2xl md:text-3xl font-light mb-4 text-gray-100"
          >
            {t('hero.heading')}
          </motion.h2>

          {/* Subtitle */}
          <motion.p
            initial={{opacity: 0, y: 30}}
            animate={{opacity: 1, y: 0}}
            transition={{duration: 0.6, delay: 0.6}}
            className="text-xl md:text-2xl font-light text-gray-200 mb-12 leading-relaxed"
          >
            {t('hero.subtitle')}
          </motion.p>

          {/* Email Form */}
          <EmailForm />
        </motion.div>

        {/* Footer */}
        <motion.footer
          initial={{opacity: 0}}
          animate={{opacity: 1}}
          transition={{delay: 1.2, duration: 0.6}}
          className="absolute bottom-8 left-0 right-0 text-center"
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 text-gray-300">
            <a
              href="#"
              className="hover:text-white transition-colors duration-300 flex items-center gap-2"
            >
              <span>📸</span>
              {t('footer.instagram')}
            </a>
            <span className="hidden sm:block">•</span>
            <p>{t('footer.copyright')}</p>
          </div>
        </motion.footer>
      </div>

      {/* Floating Elements for Visual Interest */}
      <motion.div
        animate={{
          y: [0, -20, 0],
          opacity: [0.3, 0.6, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        className="absolute top-1/4 left-10 w-2 h-2 bg-white rounded-full hidden md:block"
      />
      <motion.div
        animate={{
          y: [0, -15, 0],
          opacity: [0.2, 0.5, 0.2],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 1,
        }}
        className="absolute top-1/3 right-16 w-1 h-1 bg-white rounded-full hidden md:block"
      />
      <motion.div
        animate={{
          y: [0, -25, 0],
          opacity: [0.4, 0.7, 0.4],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: 'easeInOut',
          delay: 2,
        }}
        className="absolute bottom-1/3 left-1/4 w-1.5 h-1.5 bg-white rounded-full hidden md:block"
      />
    </div>
  );
}
