/**
 * Centralized internationalization configuration
 * Used by middleware, layouts, and components
 */

export const locales = ['en', 'es', 'fr', 'pl', 'ru'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

/**
 * Language display configuration for UI components
 */
export const languageConfig = [
  { code: 'en' as const, name: 'English', flag: '🇺🇸' },
  { code: 'es' as const, name: '<PERSON>spa<PERSON><PERSON>', flag: '🇨🇷' },
  { code: 'fr' as const, name: 'Français', flag: '🇫🇷' },
  { code: 'pl' as const, name: '<PERSON><PERSON>', flag: '🇵🇱' },
  { code: 'ru' as const, name: 'Русский', flag: '🇷🇺' },
] as const;

/**
 * Check if a locale is supported
 */
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

/**
 * Get language display info by locale code
 */
export function getLanguageInfo(locale: Locale) {
  return languageConfig.find(lang => lang.code === locale);
}

/**
 * Get all supported locales
 */
export function getSupportedLocales(): readonly Locale[] {
  return locales;
}
