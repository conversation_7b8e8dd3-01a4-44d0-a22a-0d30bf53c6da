/**
 * Language persistence utilities for storing and retrieving user language preferences
 */

const LANGUAGE_COOKIE_NAME = 'preferred-language';
const LANGUAGE_STORAGE_KEY = 'ojochal-language';

export const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'] as const;
export type SupportedLocale = (typeof supportedLocales)[number];

/**
 * Get the user's preferred language from various sources in order of priority:
 * 1. Previously saved preference (cookie/localStorage)
 * 2. Browser language preference
 * 3. Default to English
 */
export function getPreferredLanguage(): SupportedLocale {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return 'en'; // Default for SSR
  }

  // 1. Check saved preference (cookie first, then localStorage)
  const savedLanguage = getSavedLanguage();
  if (
    savedLanguage &&
    supportedLocales.includes(savedLanguage as SupportedLocale)
  ) {
    return savedLanguage as SupportedLocale;
  }

  // 2. Check browser language preference
  const browserLanguage = getBrowserLanguage();
  if (
    browserLanguage &&
    supportedLocales.includes(browserLanguage as SupportedLocale)
  ) {
    return browserLanguage as SupportedLocale;
  }

  // 3. Default to English
  return 'en';
}

/**
 * Save the user's language preference to both cookie and localStorage
 */
export function saveLanguagePreference(locale: SupportedLocale): void {
  if (typeof window === 'undefined') return;

  // Save to cookie (for server-side access)
  document.cookie = `${LANGUAGE_COOKIE_NAME}=${locale}; path=/; max-age=${
    60 * 60 * 24 * 365
  }; SameSite=lax`;

  // Save to localStorage (for client-side access)
  try {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, locale);
  } catch (error) {
    console.warn('Failed to save language preference to localStorage:', error);
  }
}

/**
 * Get saved language preference from cookie or localStorage
 */
function getSavedLanguage(): string | null {
  if (typeof window === 'undefined') return null;

  // Try cookie first
  const cookieValue = getCookieValue(LANGUAGE_COOKIE_NAME);
  if (cookieValue) return cookieValue;

  // Try localStorage
  try {
    return localStorage.getItem(LANGUAGE_STORAGE_KEY);
  } catch (error) {
    console.warn(
      'Failed to read language preference from localStorage:',
      error
    );
    return null;
  }
}

/**
 * Get browser language preference
 */
function getBrowserLanguage(): string | null {
  if (typeof window === 'undefined') return null;

  // Get the primary language from browser
  const browserLang =
    navigator.language ||
    (navigator as unknown as {userLanguage?: string}).userLanguage;
  if (!browserLang) return null;

  // Extract the language code (e.g., 'en-US' -> 'en')
  const langCode = browserLang.split('-')[0].toLowerCase();

  // Map some common language codes to our supported locales
  const languageMap: Record<string, SupportedLocale> = {
    en: 'en',
    es: 'es',
    fr: 'fr',
    pl: 'pl',
    ru: 'ru',
    spanish: 'es',
    french: 'fr',
    polish: 'pl',
    russian: 'ru',
  };

  return languageMap[langCode] || null;
}

/**
 * Get cookie value by name
 */
function getCookieValue(name: string): string | null {
  if (typeof document === 'undefined') return null;

  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    return parts.pop()?.split(';').shift() || null;
  }
  return null;
}

/**
 * Clear saved language preference
 */
export function clearLanguagePreference(): void {
  if (typeof window === 'undefined') return;

  // Clear cookie
  document.cookie = `${LANGUAGE_COOKIE_NAME}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;

  // Clear localStorage
  try {
    localStorage.removeItem(LANGUAGE_STORAGE_KEY);
  } catch (error) {
    console.warn(
      'Failed to clear language preference from localStorage:',
      error
    );
  }
}
