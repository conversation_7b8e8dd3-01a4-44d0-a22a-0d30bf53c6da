# Ojochal.cr - Coming Soon Landing Page

A beautiful, animated coming soon landing page for Ojochal, Costa Rica's hidden gem. Built with Next.js 15, Tailwind CSS, Framer Motion, and next-intl for internationalization.

## ✨ Features

- **🌍 Internationalization**: English and Costa Rican Spanish with next-intl
- **🎨 Beautiful Animations**: Smooth Framer Motion animations
- **📱 Responsive Design**: Optimized for all devices
- **📧 Email Collection**: Mailchimp integration for newsletter signups
- **🔍 SEO Optimized**: Meta tags, Open Graph, and structured data
- **⚡ Performance**: Built with Next.js 15 App Router for optimal performance

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:

```bash
git clone <your-repo-url>
cd ojochal.cr
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

4. Configure your Mailchimp settings in `.env.local`:

```env
MAILCHIMP_API_KEY=your_mailchimp_api_key_here
MAILCHIMP_AUDIENCE_ID=your_audience_id_here
MAILCHIMP_SERVER_PREFIX=us1
```

5. Run the development server:

```bash
npm run dev
```

6. Open [http://localhost:3000](http://localhost:3000) to see the result.

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Internationalization**: next-intl
- **Email**: Mailchimp API integration
- **TypeScript**: Full type safety
- **Deployment**: Optimized for Vercel

## 🌐 Internationalization

The site supports:

- **English** (default): `/en` or `/`
- **Costa Rican Spanish**: `/es`

Language toggle is available in the top-right corner.

## 📧 Mailchimp Setup

1. Create a Mailchimp account and audience
2. Get your API key from Account > Extras > API keys
3. Find your Audience ID in Audience > Settings > Audience name and defaults
4. Update the environment variables in `.env.local`

## 🎨 Customization

### Images

- Add your hero background image to `/public/ojochal-hero.jpg`
- Update the Open Graph image at `/public/og-image.jpg`
- Replace the favicon at `/public/favicon.ico`

### Content

- Edit translations in `/messages/en.json` and `/messages/es.json`
- Modify the hero section in `/src/app/[locale]/page.tsx`

### Styling

- Customize colors and animations in the component files
- Update global styles in `/src/app/globals.css`

## 📱 Responsive Design

The landing page is fully responsive with:

- Mobile-first design approach
- Optimized touch interactions
- Adaptive typography and spacing
- Cross-browser compatibility

## 🔍 SEO Features

- Dynamic meta tags based on locale
- Open Graph and Twitter Card support
- Structured data for search engines
- Optimized Core Web Vitals
- Lighthouse-friendly performance

## 🚀 Deployment

### Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

### Other Platforms

The app can be deployed to any platform that supports Next.js:

- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.
