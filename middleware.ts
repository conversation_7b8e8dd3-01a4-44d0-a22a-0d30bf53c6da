import createMiddleware from 'next-intl/middleware';
import {NextRequest} from 'next/server';

const intlMiddleware = createMiddleware({
  // A list of all locales that are supported
  locales: ['en', 'es', 'fr', 'pl', 'ru'],

  // Used when no locale matches
  defaultLocale: 'en',

  // Don't show the default locale in the URL
  localePrefix: 'as-needed',

  // Disable automatic locale detection to prevent conflicts
  localeDetection: false,
});

function getBrowserLanguage(acceptLanguage: string | null): string | null {
  if (!acceptLanguage) return null;

  const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'];
  const languages = acceptLanguage.split(',').map((lang) => {
    const [code] = lang.trim().split(';')[0].split('-');
    return code.toLowerCase();
  });

  return languages.find((lang) => supportedLocales.includes(lang)) || null;
}

export default function middleware(request: NextRequest) {
  const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'];

  // Only handle root path for language detection
  if (request.nextUrl.pathname === '/') {
    // Check if user has a saved language preference cookie
    const savedLanguage = request.cookies.get('preferred-language')?.value;

    if (savedLanguage && supportedLocales.includes(savedLanguage)) {
      // Respect saved preference
      if (savedLanguage !== 'en') {
        return Response.redirect(new URL(`/${savedLanguage}`, request.url));
      }
    } else {
      // No saved preference, check browser language
      const browserLanguage = getBrowserLanguage(
        request.headers.get('accept-language')
      );
      if (browserLanguage && browserLanguage !== 'en') {
        return Response.redirect(new URL(`/${browserLanguage}`, request.url));
      }
    }
  }

  // Use default next-intl middleware for all other cases
  return intlMiddleware(request);
}

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/((?!api|_next|_vercel|.*\\..*).*)'],
};
