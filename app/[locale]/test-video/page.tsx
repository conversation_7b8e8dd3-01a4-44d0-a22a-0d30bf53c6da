'use client';

export default function TestVideoPage() {
  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="w-full max-w-4xl">
        <h1 className="text-white text-2xl mb-4">Video Test Page</h1>
        
        {/* Simple video element */}
        <video
          autoPlay
          loop
          muted
          playsInline
          controls
          className="w-full h-auto"
          onLoadedData={() => console.log('Video loaded successfully')}
          onError={(e) => console.error('Video error:', e)}
          onCanPlay={() => console.log('Video can play')}
        >
          <source src="/hero-coming-soon.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>
        
        <p className="text-white mt-4">
          If you see this video playing, the file is working correctly.
        </p>
      </div>
    </div>
  );
}
