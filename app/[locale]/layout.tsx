import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from 'next-intl/server';
import {notFound} from 'next/navigation';
import {Inter} from 'next/font/google';
import type {Metadata} from 'next';
import {locales, isValidLocale} from '@/lib/i18n-config';
import '../globals.css';

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
});

type Props = {
  children: React.ReactNode;
  params: Promise<{locale: string}>;
};

export async function generateMetadata({params}: Props): Promise<Metadata> {
  const {locale} = await params;

  // Import messages for SEO
  const messages = await getMessages({locale});
  const seoMessages = messages.seo as any;

  // Base URL for canonical URLs
  const baseUrl = 'https://ojochal.cr';

  // Generate canonical URL
  const canonicalUrl = locale === 'en' ? baseUrl : `${baseUrl}/${locale}`;

  // Generate alternate URLs for all languages
  const alternateLanguages: Record<string, string> = {};
  locales.forEach((loc) => {
    alternateLanguages[loc] = loc === 'en' ? baseUrl : `${baseUrl}/${loc}`;
  });

  return {
    title:
      seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',
    description:
      seoMessages?.description ||
      'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',

    // Canonical URL
    alternates: {
      canonical: canonicalUrl,
      languages: alternateLanguages,
    },

    openGraph: {
      title:
        seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',
      description:
        seoMessages?.description ||
        'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',
      url: canonicalUrl,
      siteName: 'Ojochal.cr',
      images: [
        {
          url: '/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'Ojochal, Costa Rica - Hidden Gem',
        },
      ],
      locale: locale,
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title:
        seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',
      description:
        seoMessages?.description ||
        'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',
      images: ['/og-image.jpg'],
    },

    icons: {
      icon: [
        {url: '/favicon.svg', type: 'image/svg+xml'},
        {url: '/favicon.ico', sizes: '32x32'},
      ],
    },

    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function LocaleLayout({children, params}: Props) {
  const {locale} = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!isValidLocale(locale)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages({locale});

  return (
    <html lang={locale} className={inter.className}>
      <body className="antialiased">
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
