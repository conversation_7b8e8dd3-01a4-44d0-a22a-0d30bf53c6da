/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["rect", { width: "18", height: "18", x: "3", y: "3", rx: "2", key: "afitv7" }],
  ["path", { d: "m14 16-4-4 4-4", key: "ojs7w8" }]
];
const SquareChevronLeft = createLucideIcon("square-chevron-left", __iconNode);

export { __iconNode, SquareChevronLeft as default };
//# sourceMappingURL=square-chevron-left.js.map
