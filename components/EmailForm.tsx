'use client';

import {useState, useEffect} from 'react';
import {motion, AnimatePresence} from 'framer-motion';
import {useTranslations} from 'next-intl';
import {Button} from '@/components/ui/button';
import {Input} from '@/components/ui/input';
import {Card} from '@/components/ui/card';
import {Loader2, CheckCircle, AlertCircle} from 'lucide-react';

export default function EmailForm() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<
    'idle' | 'loading' | 'success' | 'error'
  >('idle');
  const [mounted, setMounted] = useState(false);
  const t = useTranslations('hero');

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="w-full max-w-md mx-auto">
        <div className="p-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1 h-12 bg-transparent border-none rounded" />
            <div className="px-6 py-3 bg-white text-gray-900 rounded-xl h-12 min-w-[120px]" />
          </div>
        </div>
      </div>
    );
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setStatus('loading');

    try {
      // Replace with your actual Mailchimp form action URL
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({email}),
      });

      if (response.ok) {
        setStatus('success');
        setEmail('');
      } else {
        setStatus('error');
      }
    } catch (error) {
      setStatus('error');
    }

    // Reset status after 3 seconds
    setTimeout(() => {
      setStatus('idle');
    }, 3000);
  };

  return (
    <motion.div
      initial={{opacity: 0, y: 30}}
      animate={{opacity: 1, y: 0}}
      transition={{delay: 0.8, duration: 0.6}}
      className="w-full max-w-md mx-auto"
    >
      <AnimatePresence mode="wait">
        {status === 'success' ? (
          <motion.div
            key="success"
            initial={{opacity: 0, scale: 0.8}}
            animate={{opacity: 1, scale: 1}}
            exit={{opacity: 0, scale: 0.8}}
          >
            <Card className="p-6 bg-green-500/20 backdrop-blur-md border-green-400/30 text-center">
              <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-4" />
              <p className="text-white font-medium">{t('successMessage')}</p>
            </Card>
          </motion.div>
        ) : (
          <motion.form
            key="form"
            onSubmit={handleSubmit}
            initial={{opacity: 0, scale: 0.8}}
            animate={{opacity: 1, scale: 1}}
            exit={{opacity: 0, scale: 0.8}}
          >
            <Card className="p-2 bg-white/10 backdrop-blur-md border-white/20">
              {/* Honeypot field for anti-bot protection */}
              <input
                type="text"
                name="b_honeypot"
                tabIndex={-1}
                className="absolute left-[-9999px]"
                aria-hidden="true"
              />

              <div className="flex flex-col sm:flex-row gap-3">
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder={t('emailPlaceholder')}
                  required
                  disabled={status === 'loading'}
                  className="flex-1 bg-transparent text-white placeholder-white/70 border-none focus:ring-0 focus:ring-offset-0 text-lg"
                />

                <Button
                  type="submit"
                  disabled={status === 'loading' || !email}
                  className="px-6 py-3 bg-white text-gray-900 font-semibold hover:bg-gray-100 disabled:opacity-50 whitespace-nowrap"
                >
                  {status === 'loading' ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      <span>...</span>
                    </>
                  ) : (
                    t('submitButton')
                  )}
                </Button>
              </div>
            </Card>
          </motion.form>
        )}
      </AnimatePresence>

      {status === 'error' && (
        <motion.div
          initial={{opacity: 0, y: 10}}
          animate={{opacity: 1, y: 0}}
          className="mt-3 flex items-center justify-center gap-2 text-red-300 text-sm"
        >
          <AlertCircle className="h-4 w-4" />
          <span>{t('errorMessage')}</span>
        </motion.div>
      )}
    </motion.div>
  );
}
