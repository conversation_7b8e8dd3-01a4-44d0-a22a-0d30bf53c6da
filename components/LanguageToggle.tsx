'use client';

import {useLocale} from 'next-intl';
import {useRouter, usePathname} from 'next/navigation';
import {motion} from 'framer-motion';
import {Button} from '@/components/ui/button';
import {ChevronDown} from 'lucide-react';
import {useState} from 'react';

const languages = [
  {code: 'en', name: 'English', flag: '🇺🇸'},
  {code: 'es', name: 'Españo<PERSON>', flag: '🇨🇷'},
  {code: 'fr', name: 'Français', flag: '🇫🇷'},
  {code: 'pl', name: '<PERSON><PERSON>', flag: '🇵🇱'},
  {code: 'ru', name: 'Русский', flag: '🇷🇺'},
];

export default function LanguageToggle() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const currentLanguage = languages.find(lang => lang.code === locale);

  const switchLanguage = (targetLocale: string) => {
    if (targetLocale === 'en') {
      // Switch to English - use absolute path to root
      router.push('/');
    } else {
      // Switch to other language
      router.push(`/${targetLocale}${pathname}`);
    }
    setIsOpen(false);
  };

  return (
    <motion.div
      initial={{opacity: 0, y: -20}}
      animate={{opacity: 1, y: 0}}
      transition={{delay: 0.5}}
      className="fixed top-6 right-6 z-50"
    >
      <div className="relative">
        <Button
          variant="outline"
          onClick={() => setIsOpen(!isOpen)}
          className="bg-white/20 backdrop-blur-md border-white/30 text-white hover:bg-white/30 hover:text-white min-w-[120px] justify-between"
        >
          <span className="flex items-center gap-2">
            <span>{currentLanguage?.flag}</span>
            <span className="hidden sm:inline">{currentLanguage?.name}</span>
            <span className="sm:hidden">{currentLanguage?.code.toUpperCase()}</span>
          </span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </Button>

        {isOpen && (
          <motion.div
            initial={{opacity: 0, y: -10}}
            animate={{opacity: 1, y: 0}}
            exit={{opacity: 0, y: -10}}
            className="absolute top-full mt-2 right-0 bg-white/95 backdrop-blur-md border border-white/30 rounded-lg shadow-lg overflow-hidden min-w-[160px]"
          >
            {languages.map(language => (
              <button
                key={language.code}
                onClick={() => switchLanguage(language.code)}
                className={`w-full px-4 py-3 text-left hover:bg-black/10 transition-colors flex items-center gap-3 ${
                  locale === language.code ? 'bg-black/5 font-medium' : ''
                }`}
              >
                <span className="text-lg">{language.flag}</span>
                <span className="text-gray-800">{language.name}</span>
              </button>
            ))}
          </motion.div>
        )}
      </div>

      {/* Backdrop to close dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 -z-10"
          onClick={() => setIsOpen(false)}
        />
      )}
    </motion.div>
  );
}
