'use client';

import {useState, useRef, useEffect} from 'react';
import {motion} from 'framer-motion';

interface VideoBackgroundProps {
  src: string;
  fallbackGradient?: string;
}

export default function VideoBackground({
  src,
  fallbackGradient = 'bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-800',
}: VideoBackgroundProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  console.log('VideoBackground: Component rendered with src:', src);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedData = () => {
      setIsLoaded(true);
    };

    const handleError = (e: Event) => {
      setHasError(true);
    };

    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('error', handleError);

    // Also check if video is already loaded
    if (video.readyState >= 3) {
      setIsLoaded(true);
    }

    return () => {
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('error', handleError);
    };
  }, [src]);

  return (
    <div className="video-background absolute inset-0 z-0">
      {/* Fallback Background */}
      <div className={`absolute inset-0 ${fallbackGradient}`} />

      {/* Video Background */}
      {!hasError && (
        <video
          ref={videoRef}
          autoPlay
          loop
          muted
          playsInline
          preload="metadata"
          className="absolute inset-0 w-full h-full object-cover transition-opacity duration-1000"
          style={{
            filter: 'brightness(0.8) contrast(1.1)',
            opacity: isLoaded ? 1 : 0,
          }}
        >
          <source src={src} type="video/mp4" />
        </video>
      )}

      {/* Dark Overlay */}
      <div className="dark-overlay absolute inset-0 bg-black/50" />

      {/* Grid Overlay */}
      <div className="grid-overlay absolute inset-0 opacity-15">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 255, 255, 0.15) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 255, 255, 0.15) 1px, transparent 1px)
            `,
            backgroundSize: '2px 2px',
          }}
        />
      </div>

      {/* Vignette Effect */}
      <div className="vignette absolute inset-0 bg-gradient-radial from-transparent via-transparent to-black/30" />

      {/* Animated Scale Effect */}
      <motion.div
        initial={{scale: 1.1}}
        animate={{scale: 1}}
        transition={{duration: 10, ease: 'easeOut'}}
        className="scale-effect absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/20"
      />

      {/* Loading Indicator */}
      {!isLoaded && !hasError && (
        <div className="loading absolute inset-0 flex items-end justify-end !p-4">
          <motion.div
            animate={{rotate: 360}}
            transition={{duration: 2, repeat: Infinity, ease: 'linear'}}
            className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
          />
        </div>
      )}

      {/* Error Indicator */}
      {hasError && (
        <div className="error absolute inset-0 flex items-end justify-end !p-4">
          <div className="bg-red-500/80 text-white text-xs px-2 py-1 rounded">
            Video failed to load
          </div>
        </div>
      )}
    </div>
  );
}
