'use client';

import {useEffect} from 'react';
import {useLocale} from 'next-intl';
import {saveLanguagePreference} from '@/lib/language-persistence';
import {type Locale} from '@/lib/i18n-config';

/**
 * Client-side language detection component
 * Handles initial language preference detection and persistence
 */
export default function LanguageDetector() {
  const currentLocale = useLocale();

  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    // Save the current locale as the user's preference
    // This ensures that when they visit a specific language page,
    // it gets saved as their preference for future visits
    saveLanguagePreference(currentLocale as Locale);
  }, [currentLocale]);

  // This component doesn't render anything
  return null;
}
