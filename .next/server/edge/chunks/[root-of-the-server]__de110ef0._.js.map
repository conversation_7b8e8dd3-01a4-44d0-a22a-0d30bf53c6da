{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport {NextRequest} from 'next/server';\n\nconst intlMiddleware = createMiddleware({\n  // A list of all locales that are supported\n  locales: ['en', 'es', 'fr', 'pl', 'ru'],\n\n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // Don't show the default locale in the URL\n  localePrefix: 'as-needed',\n\n  // Disable automatic locale detection to prevent conflicts\n  localeDetection: false,\n});\n\nfunction getBrowserLanguage(acceptLanguage: string | null): string | null {\n  if (!acceptLanguage) return null;\n\n  const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'];\n  const languages = acceptLanguage.split(',').map((lang) => {\n    const [code] = lang.trim().split(';')[0].split('-');\n    return code.toLowerCase();\n  });\n\n  return languages.find((lang) => supportedLocales.includes(lang)) || null;\n}\n\nexport default function middleware(request: NextRequest) {\n  const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'];\n\n  // Only handle root path for language detection\n  if (request.nextUrl.pathname === '/') {\n    // Check if user has a saved language preference cookie\n    const savedLanguage = request.cookies.get('preferred-language')?.value;\n\n    if (savedLanguage && supportedLocales.includes(savedLanguage)) {\n      // Respect saved preference\n      if (savedLanguage !== 'en') {\n        return Response.redirect(new URL(`/${savedLanguage}`, request.url));\n      }\n    } else {\n      // No saved preference, check browser language\n      const browserLanguage = getBrowserLanguage(\n        request.headers.get('accept-language')\n      );\n      if (browserLanguage && browserLanguage !== 'en') {\n        return Response.redirect(new URL(`/${browserLanguage}`, request.url));\n      }\n    }\n  }\n\n  // Use default next-intl middleware for all other cases\n  return intlMiddleware(request);\n}\n\nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/', '/((?!api|_next|_vercel|.*\\\\..*).*)'],\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,iBAAiB,CAAA,GAAA,4TAAA,CAAA,UAAgB,AAAD,EAAE;IACtC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvC,8BAA8B;IAC9B,eAAe;IAEf,2CAA2C;IAC3C,cAAc;IAEd,0DAA0D;IAC1D,iBAAiB;AACnB;AAEA,SAAS,mBAAmB,cAA6B;IACvD,IAAI,CAAC,gBAAgB,OAAO;IAE5B,MAAM,mBAAmB;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IACvD,MAAM,YAAY,eAAe,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;QAC/C,MAAM,CAAC,KAAK,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;QAC/C,OAAO,KAAK,WAAW;IACzB;IAEA,OAAO,UAAU,IAAI,CAAC,CAAC,OAAS,iBAAiB,QAAQ,CAAC,UAAU;AACtE;AAEe,SAAS,WAAW,OAAoB;IACrD,MAAM,mBAAmB;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvD,+CAA+C;IAC/C,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,KAAK;QACpC,uDAAuD;QACvD,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC,uBAAuB;QAEjE,IAAI,iBAAiB,iBAAiB,QAAQ,CAAC,gBAAgB;YAC7D,2BAA2B;YAC3B,IAAI,kBAAkB,MAAM;gBAC1B,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,eAAe,EAAE,QAAQ,GAAG;YACnE;QACF,OAAO;YACL,8CAA8C;YAC9C,MAAM,kBAAkB,mBACtB,QAAQ,OAAO,CAAC,GAAG,CAAC;YAEtB,IAAI,mBAAmB,oBAAoB,MAAM;gBAC/C,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,iBAAiB,EAAE,QAAQ,GAAG;YACrE;QACF;IACF;IAEA,uDAAuD;IACvD,OAAO,eAAe;AACxB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;QAAK;KAAqC;AACtD"}}]}