{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport {NextRequest} from 'next/server';\n\nconst intlMiddleware = createMiddleware({\n  // A list of all locales that are supported\n  locales: ['en', 'es', 'fr', 'pl', 'ru'],\n\n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // Don't show the default locale in the URL\n  localePrefix: 'as-needed',\n\n  // Disable automatic locale detection to prevent conflicts\n  localeDetection: false,\n});\n\nfunction getBrowserLanguage(acceptLanguage: string | null): string | null {\n  if (!acceptLanguage) return null;\n\n  const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'];\n\n  // Parse Accept-Language header with quality factors\n  const languages = acceptLanguage\n    .split(',')\n    .map((lang) => {\n      const parts = lang.trim().split(';');\n      const code = parts[0].split('-')[0].toLowerCase();\n      const quality = parts[1] ? parseFloat(parts[1].split('=')[1]) : 1.0;\n      return {code, quality};\n    })\n    .filter(({code}) => supportedLocales.includes(code))\n    .sort((a, b) => b.quality - a.quality); // Sort by quality (highest first)\n\n  return languages.length > 0 ? languages[0].code : null;\n}\n\nexport default function middleware(request: NextRequest) {\n  const supportedLocales = ['en', 'es', 'fr', 'pl', 'ru'];\n\n  // Only handle root path for language detection\n  if (request.nextUrl.pathname === '/') {\n    // Check if user has a saved language preference cookie\n    const savedLanguage = request.cookies.get('preferred-language')?.value;\n\n    if (savedLanguage && supportedLocales.includes(savedLanguage)) {\n      // Respect saved preference\n      if (savedLanguage !== 'en') {\n        return Response.redirect(new URL(`/${savedLanguage}`, request.url));\n      }\n    } else {\n      // No saved preference, check browser language\n      const browserLanguage = getBrowserLanguage(\n        request.headers.get('accept-language')\n      );\n      if (browserLanguage && browserLanguage !== 'en') {\n        return Response.redirect(new URL(`/${browserLanguage}`, request.url));\n      }\n    }\n  }\n\n  // Use default next-intl middleware for all other cases\n  return intlMiddleware(request);\n}\n\nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/', '/((?!api|_next|_vercel|.*\\\\..*).*)'],\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAGA,MAAM,iBAAiB,CAAA,GAAA,4TAAA,CAAA,UAAgB,AAAD,EAAE;IACtC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvC,8BAA8B;IAC9B,eAAe;IAEf,2CAA2C;IAC3C,cAAc;IAEd,0DAA0D;IAC1D,iBAAiB;AACnB;AAEA,SAAS,mBAAmB,cAA6B;IACvD,IAAI,CAAC,gBAAgB,OAAO;IAE5B,MAAM,mBAAmB;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvD,oDAAoD;IACpD,MAAM,YAAY,eACf,KAAK,CAAC,KACN,GAAG,CAAC,CAAC;QACJ,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;QAChC,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW;QAC/C,MAAM,UAAU,KAAK,CAAC,EAAE,GAAG,WAAW,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;QAChE,OAAO;YAAC;YAAM;QAAO;IACvB,GACC,MAAM,CAAC,CAAC,EAAC,IAAI,EAAC,GAAK,iBAAiB,QAAQ,CAAC,OAC7C,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,kCAAkC;IAE5E,OAAO,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,CAAC,IAAI,GAAG;AACpD;AAEe,SAAS,WAAW,OAAoB;IACrD,MAAM,mBAAmB;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvD,+CAA+C;IAC/C,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,KAAK;QACpC,uDAAuD;QACvD,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC,uBAAuB;QAEjE,IAAI,iBAAiB,iBAAiB,QAAQ,CAAC,gBAAgB;YAC7D,2BAA2B;YAC3B,IAAI,kBAAkB,MAAM;gBAC1B,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,eAAe,EAAE,QAAQ,GAAG;YACnE;QACF,OAAO;YACL,8CAA8C;YAC9C,MAAM,kBAAkB,mBACtB,QAAQ,OAAO,CAAC,GAAG,CAAC;YAEtB,IAAI,mBAAmB,oBAAoB,MAAM;gBAC/C,OAAO,SAAS,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,iBAAiB,EAAE,QAAQ,GAAG;YACrE;QACF;IACF;IAEA,uDAAuD;IACvD,OAAO,eAAe;AACxB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;QAAK;KAAqC;AACtD"}}]}