{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\n\nexport default createMiddleware({\n  // A list of all locales that are supported\n  locales: ['en', 'es', 'fr', 'pl', 'ru'],\n\n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // Don't show the default locale in the URL\n  localePrefix: 'as-needed',\n  \n  // Disable automatic locale detection to prevent browser language redirects\n  localeDetection: false,\n});\n\nexport const config = {\n  // Match only internationalized pathnames\n  matcher: ['/', '/((?!api|_next|_vercel|.*\\\\..*).*)'],\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,4TAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,2CAA2C;IAC3C,SAAS;QAAC;QAAM;QAAM;QAAM;QAAM;KAAK;IAEvC,8BAA8B;IAC9B,eAAe;IAEf,2CAA2C;IAC3C,cAAc;IAEd,2EAA2E;IAC3E,iBAAiB;AACnB;AAEO,MAAM,SAAS;IACpB,yCAAyC;IACzC,SAAS;QAAC;QAAK;KAAqC;AACtD"}}]}