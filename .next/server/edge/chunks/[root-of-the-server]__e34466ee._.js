(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__e34466ee._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/lib/i18n-config.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Centralized internationalization configuration
 * Used by middleware, layouts, and components
 */ __turbopack_context__.s({
    "defaultLocale": (()=>defaultLocale),
    "getLanguageInfo": (()=>getLanguageInfo),
    "getSupportedLocales": (()=>getSupportedLocales),
    "isValidLocale": (()=>isValidLocale),
    "languageConfig": (()=>languageConfig),
    "locales": (()=>locales)
});
const locales = [
    'en',
    'es',
    'fr',
    'pl',
    'ru'
];
const defaultLocale = 'en';
const languageConfig = [
    {
        code: 'en',
        name: 'English',
        flag: '🇺🇸'
    },
    {
        code: 'es',
        name: 'Español',
        flag: '🇨🇷'
    },
    {
        code: 'fr',
        name: 'Français',
        flag: '🇫🇷'
    },
    {
        code: 'pl',
        name: 'Polski',
        flag: '🇵🇱'
    },
    {
        code: 'ru',
        name: 'Русский',
        flag: '🇷🇺'
    }
];
function isValidLocale(locale) {
    return locales.includes(locale);
}
function getLanguageInfo(locale) {
    return languageConfig.find((lang)=>lang.code === locale);
}
function getSupportedLocales() {
    return locales;
}
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$3_next$40$15$2e$3$2e$4_react$40$19$2e$1$2e$0_typescript$40$5$2e$8$2e$3$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next-intl@4.3.3_next@15.3.4_react@19.1.0_typescript@5.8.3/node_modules/next-intl/dist/esm/development/middleware/middleware.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$i18n$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/i18n-config.ts [middleware-edge] (ecmascript)");
;
;
const intlMiddleware = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$2d$intl$40$4$2e$3$2e$3_next$40$15$2e$3$2e$4_react$40$19$2e$1$2e$0_typescript$40$5$2e$8$2e$3$2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$middleware$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["default"])({
    // A list of all locales that are supported
    locales: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$i18n$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["locales"],
    // Used when no locale matches
    defaultLocale: __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$i18n$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["defaultLocale"],
    // Don't show the default locale in the URL
    localePrefix: 'as-needed',
    // Disable automatic locale detection to prevent conflicts
    localeDetection: false
});
function getBrowserLanguage(acceptLanguage) {
    if (!acceptLanguage) return null;
    // Parse Accept-Language header with quality factors
    const languages = acceptLanguage.split(',').map((lang)=>{
        const parts = lang.trim().split(';');
        const code = parts[0].split('-')[0].toLowerCase();
        const quality = parts[1] ? parseFloat(parts[1].split('=')[1]) : 1.0;
        return {
            code,
            quality
        };
    }).filter(({ code })=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$i18n$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isValidLocale"])(code)).sort((a, b)=>b.quality - a.quality); // Sort by quality (highest first)
    return languages.length > 0 ? languages[0].code : null;
}
function middleware(request) {
    // Only handle root path for language detection
    if (request.nextUrl.pathname === '/') {
        // Check if user has a saved language preference cookie
        const savedLanguage = request.cookies.get('preferred-language')?.value;
        if (savedLanguage && (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$i18n$2d$config$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["isValidLocale"])(savedLanguage)) {
            // Respect saved preference
            if (savedLanguage !== 'en') {
                return Response.redirect(new URL(`/${savedLanguage}`, request.url));
            }
        } else {
            // No saved preference, check browser language
            const browserLanguage = getBrowserLanguage(request.headers.get('accept-language'));
            if (browserLanguage && browserLanguage !== 'en') {
                return Response.redirect(new URL(`/${browserLanguage}`, request.url));
            }
        }
    }
    // Use default next-intl middleware for all other cases
    return intlMiddleware(request);
}
const config = {
    // Match only internationalized pathnames
    matcher: [
        '/',
        '/((?!api|_next|_vercel|.*\\..*).*)'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__e34466ee._.js.map