{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4437af05._.js", "server/edge/chunks/[root-of-the-server]__e34466ee._.js", "server/edge/chunks/edge-wrapper_731e4e36.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "61a4ace8121b42fc0f264b79906aac9b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "698a5f226eb354ebb87050779ad05526a86343b306c3f251322175823a2e0776", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "54e1e43531b39b299b9acc15705155f056542dbbfd55d7ed1c6bbe8020978748"}}}, "sortedMiddleware": ["/"], "functions": {}}