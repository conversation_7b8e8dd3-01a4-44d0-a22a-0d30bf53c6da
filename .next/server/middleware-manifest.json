{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4437af05._.js", "server/edge/chunks/[root-of-the-server]__e34466ee._.js", "server/edge/chunks/edge-wrapper_731e4e36.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "579136fd1a2a5280933b81a589892703", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "155653b07f37920e7482b796f05832559474a9ddc384eb6ca0b8f8d9f0fa6289", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fdf2d22f4fb09c1bc35ec2e38d21732fcdb033a97e73e14796aa58de15aeed2d"}}}, "sortedMiddleware": ["/"], "functions": {}}