{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_4437af05._.js", "server/edge/chunks/[root-of-the-server]__e34466ee._.js", "server/edge/chunks/edge-wrapper_731e4e36.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\..*).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "89d478740f78c649d818e56327f30808", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1c8e0623387637c33ab892076c98328782ae75e97493d5a1ffa7298660cc6349", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "72a6a68f345f4a85dceffc830c4a7fc615bbbcd918b3368467cb7dfb4cc7035f"}}}, "sortedMiddleware": ["/"], "functions": {}}