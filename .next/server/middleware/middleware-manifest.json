{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1661b02b._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_c8c51982.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(es|en)/:path*{(\\\\.json)}?", "originalSource": "/(es|en)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "8e09d43f80df48c2bbb2c8c0d1358638", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9323b20ddad6cb3b035c7db91a663005e99d83abb8f40826b8c45e77880fdb3e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3da1faaaa027410625aae8ea49187d28c22be67fd498bcc10afa50044da45811"}}}, "instrumentation": null, "functions": {}}