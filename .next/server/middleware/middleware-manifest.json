{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4437af05._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_bf040a03.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "20f048258a420ec0a3f07ce07e32aef9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "071b242ba58174505ef836f45437455e0c6376f0ce24693656f471f8275baf61", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "410157400de4110f9e95e422175d20a04ddfe59893adb085c3c36d63c56d91f2"}}}, "instrumentation": null, "functions": {}}