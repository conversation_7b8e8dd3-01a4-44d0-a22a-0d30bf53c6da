{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1661b02b._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_c8c51982.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "c1a240a5795bc5e28cccdc9a6c974a9d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "404a59e506aa7434bab3df30b3b65ab87f1bb19c1563777db4c6f65a1efdddc7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ae23f91629d24ab3f859f394ba39e63253ffcf6da4b5cb9e7c5c84057c912791"}}}, "instrumentation": null, "functions": {}}