{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_4437af05._.js", "server/edge/chunks/[root-of-the-server]__e34466ee._.js", "server/edge/chunks/edge-wrapper_731e4e36.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next|_vercel|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "OdP6snfqZMEEk2bCBjnZXeIjFUlslJ85QBlVKQmWy8Q=", "__NEXT_PREVIEW_MODE_ID": "06f66e93c2c7c15c212e9610299b456c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "77f777cbb83afaf948122eb73991340e10751f20ae4999d28b01990b52c6f06a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "eb48dd10796a177bdd18adaacbf9f4654588f93219e275760f171ba2bb1ce734"}}}, "instrumentation": null, "functions": {}}