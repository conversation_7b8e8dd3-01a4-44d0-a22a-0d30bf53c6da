{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/src/components/EmailForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useTranslations } from 'next-intl';\n\nexport default function EmailForm() {\n  const [email, setEmail] = useState('');\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const t = useTranslations('hero');\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n\n    setStatus('loading');\n\n    try {\n      // Replace with your actual Mailchimp form action URL\n      const response = await fetch('/api/subscribe', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      if (response.ok) {\n        setStatus('success');\n        setEmail('');\n      } else {\n        setStatus('error');\n      }\n    } catch (error) {\n      setStatus('error');\n    }\n\n    // Reset status after 3 seconds\n    setTimeout(() => {\n      setStatus('idle');\n    }, 3000);\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 30 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ delay: 0.8, duration: 0.6 }}\n      className=\"w-full max-w-md mx-auto\"\n    >\n      <AnimatePresence mode=\"wait\">\n        {status === 'success' ? (\n          <motion.div\n            key=\"success\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.8 }}\n            className=\"text-center p-6 bg-green-500/20 backdrop-blur-md border border-green-400/30 rounded-2xl\"\n          >\n            <div className=\"text-4xl mb-2\">✨</div>\n            <p className=\"text-white font-medium\">{t('successMessage')}</p>\n          </motion.div>\n        ) : (\n          <motion.form\n            key=\"form\"\n            onSubmit={handleSubmit}\n            initial={{ opacity: 0, scale: 0.8 }}\n            animate={{ opacity: 1, scale: 1 }}\n            exit={{ opacity: 0, scale: 0.8 }}\n            className=\"flex flex-col sm:flex-row gap-3 p-2 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl\"\n          >\n            {/* Honeypot field for anti-bot protection */}\n            <input\n              type=\"text\"\n              name=\"b_honeypot\"\n              tabIndex={-1}\n              className=\"absolute left-[-9999px]\"\n              aria-hidden=\"true\"\n            />\n            \n            <input\n              type=\"email\"\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              placeholder={t('emailPlaceholder')}\n              required\n              disabled={status === 'loading'}\n              className=\"flex-1 px-4 py-3 bg-transparent text-white placeholder-white/70 border-none outline-none text-lg\"\n            />\n            \n            <motion.button\n              type=\"submit\"\n              disabled={status === 'loading' || !email}\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n              className=\"px-6 py-3 bg-white text-gray-900 font-semibold rounded-xl hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap\"\n            >\n              {status === 'loading' ? (\n                <div className=\"flex items-center gap-2\">\n                  <div className=\"w-4 h-4 border-2 border-gray-900/20 border-t-gray-900 rounded-full animate-spin\" />\n                  <span>...</span>\n                </div>\n              ) : (\n                t('submitButton')\n              )}\n            </motion.button>\n          </motion.form>\n        )}\n      </AnimatePresence>\n\n      {status === 'error' && (\n        <motion.p\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-red-300 text-center mt-3 text-sm\"\n        >\n          {t('errorMessage')}\n        </motion.p>\n      )}\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAA4C;IAC/E,MAAM,IAAI,CAAA,GAAA,oUAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,OAAO;QAEZ,UAAU;QAEV,IAAI;YACF,qDAAqD;YACrD,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,UAAU;gBACV,SAAS;YACX,OAAO;gBACL,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,UAAU;QACZ;QAEA,+BAA+B;QAC/B,WAAW;YACT,UAAU;QACZ,GAAG;IACL;IAEA,qBACE,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,OAAO;YAAK,UAAU;QAAI;QACxC,WAAU;;0BAEV,mVAAC,2SAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,WAAW,0BACV,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,WAAU;;sCAEV,mVAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,mVAAC;4BAAE,WAAU;sCAA0B,EAAE;;;;;;;mBAPrC;;;;yCAUN,mVAAC,4SAAA,CAAA,SAAM,CAAC,IAAI;oBAEV,UAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAC/B,WAAU;;sCAGV,mVAAC;4BACC,MAAK;4BACL,MAAK;4BACL,UAAU,CAAC;4BACX,WAAU;4BACV,eAAY;;;;;;sCAGd,mVAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAa,EAAE;4BACf,QAAQ;4BACR,UAAU,WAAW;4BACrB,WAAU;;;;;;sCAGZ,mVAAC,4SAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,MAAK;4BACL,UAAU,WAAW,aAAa,CAAC;4BACnC,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCAET,WAAW,0BACV,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;;;;;;kDACf,mVAAC;kDAAK;;;;;;;;;;;uCAGR,EAAE;;;;;;;mBAvCF;;;;;;;;;;YA8CT,WAAW,yBACV,mVAAC,4SAAA,CAAA,SAAM,CAAC,CAAC;gBACP,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAET,EAAE;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/src/components/LanguageToggle.tsx"], "sourcesContent": ["'use client';\n\nimport {useLocale} from 'next-intl';\nimport {useRouter, usePathname} from 'next/navigation';\nimport {motion} from 'framer-motion';\n\nexport default function LanguageToggle() {\n  const locale = useLocale();\n  const router = useRouter();\n  const pathname = usePathname();\n\n  const toggleLanguage = () => {\n    const targetLocale = locale === 'en' ? 'es' : 'en';\n\n    if (targetLocale === 'en') {\n      // Switch to English (no prefix)\n      router.push(pathname);\n    } else {\n      // Switch to Spanish (add /es prefix)\n      router.push(`/es${pathname}`);\n    }\n  };\n\n  return (\n    <motion.button\n      onClick={toggleLanguage}\n      className=\"fixed top-6 right-6 z-50 bg-white/20 backdrop-blur-md border border-white/30 rounded-full px-4 py-2 text-white font-medium hover:bg-white/30 transition-all duration-300 cursor-pointer min-w-[80px] text-center\"\n      whileHover={{scale: 1.05}}\n      whileTap={{scale: 0.95}}\n      initial={{opacity: 0, y: -20}}\n      animate={{opacity: 1, y: 0}}\n      transition={{delay: 0.5}}\n    >\n      <span className=\"whitespace-nowrap\">\n        {locale === 'en' ? '🇨🇷 ES' : '🇺🇸 EN'}\n      </span>\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,gPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,uOAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,iBAAiB;QACrB,MAAM,eAAe,WAAW,OAAO,OAAO;QAE9C,IAAI,iBAAiB,MAAM;YACzB,gCAAgC;YAChC,OAAO,IAAI,CAAC;QACd,OAAO;YACL,qCAAqC;YACrC,OAAO,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU;QAC9B;IACF;IAEA,qBACE,mVAAC,4SAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAU;QACV,YAAY;YAAC,OAAO;QAAI;QACxB,UAAU;YAAC,OAAO;QAAI;QACtB,SAAS;YAAC,SAAS;YAAG,GAAG,CAAC;QAAE;QAC5B,SAAS;YAAC,SAAS;YAAG,GAAG;QAAC;QAC1B,YAAY;YAAC,OAAO;QAAG;kBAEvB,cAAA,mVAAC;YAAK,WAAU;sBACb,WAAW,OAAO,YAAY;;;;;;;;;;;AAIvC", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport {motion} from 'framer-motion';\nimport {useTranslations} from 'next-intl';\nimport EmailForm from '@/components/EmailForm';\nimport LanguageToggle from '@/components/LanguageToggle';\n\nexport default function HomePage() {\n  const t = useTranslations();\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      {/* Background Image with Animation */}\n      <motion.div\n        initial={{scale: 1.1}}\n        animate={{scale: 1}}\n        transition={{duration: 10, ease: 'easeOut'}}\n        className=\"absolute inset-0 z-0\"\n      >\n        <div\n          className=\"w-full h-full bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-800 bg-cover bg-center bg-no-repeat\"\n          style={{\n            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), linear-gradient(135deg, #059669 0%, #0f766e 50%, #155e75 100%)`,\n          }}\n        />\n      </motion.div>\n\n      {/* Language Toggle */}\n      <LanguageToggle />\n\n      {/* Main Content */}\n      <div className=\"relative z-10 min-h-screen flex flex-col items-center justify-center px-6 text-center text-white\">\n        {/* Hero Section */}\n        <motion.div\n          initial={{opacity: 0, y: 50}}\n          animate={{opacity: 1, y: 0}}\n          transition={{duration: 0.8, delay: 0.2}}\n          className=\"max-w-4xl mx-auto mb-12\"\n        >\n          {/* Main Heading */}\n          <motion.h1\n            initial={{opacity: 0, y: 30}}\n            animate={{opacity: 1, y: 0}}\n            transition={{duration: 0.6, delay: 0.4}}\n            className=\"text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-gray-200 bg-clip-text text-transparent\"\n          >\n            {t('hero.title')}\n          </motion.h1>\n\n          {/* Discover Ojochal */}\n          <motion.h2\n            initial={{opacity: 0, y: 30}}\n            animate={{opacity: 1, y: 0}}\n            transition={{duration: 0.6, delay: 0.5}}\n            className=\"text-2xl md:text-3xl font-light mb-4 text-gray-100\"\n          >\n            {t('hero.heading')}\n          </motion.h2>\n\n          {/* Subtitle */}\n          <motion.p\n            initial={{opacity: 0, y: 30}}\n            animate={{opacity: 1, y: 0}}\n            transition={{duration: 0.6, delay: 0.6}}\n            className=\"text-xl md:text-2xl font-light text-gray-200 mb-12 leading-relaxed\"\n          >\n            {t('hero.subtitle')}\n          </motion.p>\n\n          {/* Email Form */}\n          <EmailForm />\n        </motion.div>\n\n        {/* Footer */}\n        <motion.footer\n          initial={{opacity: 0}}\n          animate={{opacity: 1}}\n          transition={{delay: 1.2, duration: 0.6}}\n          className=\"absolute bottom-8 left-0 right-0 text-center\"\n        >\n          <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4 text-gray-300\">\n            <a\n              href=\"#\"\n              className=\"hover:text-white transition-colors duration-300 flex items-center gap-2\"\n            >\n              <span>📸</span>\n              {t('footer.instagram')}\n            </a>\n            <span className=\"hidden sm:block\">•</span>\n            <p>{t('footer.copyright')}</p>\n          </div>\n        </motion.footer>\n      </div>\n\n      {/* Floating Elements for Visual Interest */}\n      <motion.div\n        animate={{\n          y: [0, -20, 0],\n          opacity: [0.3, 0.6, 0.3],\n        }}\n        transition={{\n          duration: 4,\n          repeat: Infinity,\n          ease: 'easeInOut',\n        }}\n        className=\"absolute top-1/4 left-10 w-2 h-2 bg-white rounded-full hidden md:block\"\n      />\n      <motion.div\n        animate={{\n          y: [0, -15, 0],\n          opacity: [0.2, 0.5, 0.2],\n        }}\n        transition={{\n          duration: 3,\n          repeat: Infinity,\n          ease: 'easeInOut',\n          delay: 1,\n        }}\n        className=\"absolute top-1/3 right-16 w-1 h-1 bg-white rounded-full hidden md:block\"\n      />\n      <motion.div\n        animate={{\n          y: [0, -25, 0],\n          opacity: [0.4, 0.7, 0.4],\n        }}\n        transition={{\n          duration: 5,\n          repeat: Infinity,\n          ease: 'easeInOut',\n          delay: 2,\n        }}\n        className=\"absolute bottom-1/3 left-1/4 w-1.5 h-1.5 bg-white rounded-full hidden md:block\"\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,IAAI,CAAA,GAAA,oUAAA,CAAA,kBAAe,AAAD;IAExB,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAC,OAAO;gBAAG;gBACpB,SAAS;oBAAC,OAAO;gBAAC;gBAClB,YAAY;oBAAC,UAAU;oBAAI,MAAM;gBAAS;gBAC1C,WAAU;0BAEV,cAAA,mVAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,uHAAuH,CAAC;oBAC5I;;;;;;;;;;;0BAKJ,mVAAC,oIAAA,CAAA,UAAc;;;;;0BAGf,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAC,SAAS;4BAAG,GAAG;wBAAE;wBAC3B,SAAS;4BAAC,SAAS;4BAAG,GAAG;wBAAC;wBAC1B,YAAY;4BAAC,UAAU;4BAAK,OAAO;wBAAG;wBACtC,WAAU;;0CAGV,mVAAC,4SAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAC,SAAS;oCAAG,GAAG;gCAAE;gCAC3B,SAAS;oCAAC,SAAS;oCAAG,GAAG;gCAAC;gCAC1B,YAAY;oCAAC,UAAU;oCAAK,OAAO;gCAAG;gCACtC,WAAU;0CAET,EAAE;;;;;;0CAIL,mVAAC,4SAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAC,SAAS;oCAAG,GAAG;gCAAE;gCAC3B,SAAS;oCAAC,SAAS;oCAAG,GAAG;gCAAC;gCAC1B,YAAY;oCAAC,UAAU;oCAAK,OAAO;gCAAG;gCACtC,WAAU;0CAET,EAAE;;;;;;0CAIL,mVAAC,4SAAA,CAAA,SAAM,CAAC,CAAC;gCACP,SAAS;oCAAC,SAAS;oCAAG,GAAG;gCAAE;gCAC3B,SAAS;oCAAC,SAAS;oCAAG,GAAG;gCAAC;gCAC1B,YAAY;oCAAC,UAAU;oCAAK,OAAO;gCAAG;gCACtC,WAAU;0CAET,EAAE;;;;;;0CAIL,mVAAC,+HAAA,CAAA,UAAS;;;;;;;;;;;kCAIZ,mVAAC,4SAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAC,SAAS;wBAAC;wBACpB,SAAS;4BAAC,SAAS;wBAAC;wBACpB,YAAY;4BAAC,OAAO;4BAAK,UAAU;wBAAG;wBACtC,WAAU;kCAEV,cAAA,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,mVAAC;sDAAK;;;;;;wCACL,EAAE;;;;;;;8CAEL,mVAAC;oCAAK,WAAU;8CAAkB;;;;;;8CAClC,mVAAC;8CAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,WAAU;;;;;;0BAEZ,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;gBACA,WAAU;;;;;;0BAEZ,mVAAC,4SAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBACP,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACT;gBACA,WAAU;;;;;;;;;;;;AAIlB", "debugId": null}}]}