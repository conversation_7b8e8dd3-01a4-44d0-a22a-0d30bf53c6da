{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/src/i18n/request.ts"], "sourcesContent": ["import {notFound} from 'next/navigation';\nimport {getRequestConfig} from 'next-intl/server';\n\n// Can be imported from a shared config\nconst locales = ['en', 'es'];\n\nexport default getRequestConfig(async ({locale}) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as any)) notFound();\n\n  return {\n    messages: (await import(`../../messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEA,uCAAuC;AACvC,MAAM,UAAU;IAAC;IAAM;CAAK;uCAEb,CAAA,GAAA,wXAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAC,MAAM,EAAC;IAC7C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAgB,CAAA,GAAA,0RAAA,CAAA,WAAQ,AAAD;IAE7C,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import {NextIntlClientProvider} from 'next-intl';\nimport {getMessages} from 'next-intl/server';\nimport {notFound} from 'next/navigation';\nimport type {Metadata} from 'next';\n\nconst locales = ['en', 'es'];\n\ntype Props = {\n  children: React.ReactNode;\n  params: Promise<{locale: string}>;\n};\n\nexport async function generateMetadata({params}: Props): Promise<Metadata> {\n  const {locale} = await params;\n\n  // Import messages for SEO\n  const messages = await getMessages();\n  const seoMessages = messages.seo as any;\n\n  return {\n    title:\n      seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',\n    description:\n      seoMessages?.description ||\n      'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',\n    openGraph: {\n      title:\n        seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',\n      description:\n        seoMessages?.description ||\n        'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',\n      url: 'https://ojochal.cr',\n      siteName: 'Ojochal.cr',\n      images: [\n        {\n          url: '/og-image.jpg',\n          width: 1200,\n          height: 630,\n          alt: 'Ojochal, Costa Rica - Hidden Gem',\n        },\n      ],\n      locale: locale,\n      type: 'website',\n    },\n    twitter: {\n      card: 'summary_large_image',\n      title:\n        seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',\n      description:\n        seoMessages?.description ||\n        'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',\n      images: ['/og-image.jpg'],\n    },\n    icons: {\n      icon: '/favicon.ico',\n    },\n  };\n}\n\nexport default async function LocaleLayout({children, params}: Props) {\n  const {locale} = await params;\n\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <html lang={locale}>\n      <body>\n        <NextIntlClientProvider messages={messages}>\n          {children}\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;;;;;AAGA,MAAM,UAAU;IAAC;IAAM;CAAK;AAOrB,eAAe,iBAAiB,EAAC,MAAM,EAAQ;IACpD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IAEvB,0BAA0B;IAC1B,MAAM,WAAW,MAAM,CAAA,GAAA,8WAAA,CAAA,cAAW,AAAD;IACjC,MAAM,cAAc,SAAS,GAAG;IAEhC,OAAO;QACL,OACE,aAAa,SAAS;QACxB,aACE,aAAa,eACb;QACF,WAAW;YACT,OACE,aAAa,SAAS;YACxB,aACE,aAAa,eACb;YACF,KAAK;YACL,UAAU;YACV,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,QAAQ;YACR,MAAM;QACR;QACA,SAAS;YACP,MAAM;YACN,OACE,aAAa,SAAS;YACxB,aACE,aAAa,eACb;YACF,QAAQ;gBAAC;aAAgB;QAC3B;QACA,OAAO;YACL,MAAM;QACR;IACF;AACF;AAEe,eAAe,aAAa,EAAC,QAAQ,EAAE,MAAM,EAAQ;IAClE,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IAEvB,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;QAC7B,CAAA,GAAA,0RAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,8WAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,mVAAC;QAAK,MAAM;kBACV,cAAA,mVAAC;sBACC,cAAA,mVAAC,gYAAA,CAAA,yBAAsB;gBAAC,UAAU;0BAC/B;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}