{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/app/%5Blocale%5D/test-video/page.tsx"], "sourcesContent": ["'use client';\n\nexport default function TestVideoPage() {\n  return (\n    <div className=\"min-h-screen bg-black flex items-center justify-center\">\n      <div className=\"w-full max-w-4xl\">\n        <h1 className=\"text-white text-2xl mb-4\">Video Test Page</h1>\n        \n        {/* Simple video element */}\n        <video\n          autoPlay\n          loop\n          muted\n          playsInline\n          controls\n          className=\"w-full h-auto\"\n          onLoadedData={() => console.log('Video loaded successfully')}\n          onError={(e) => console.error('Video error:', e)}\n          onCanPlay={() => console.log('Video can play')}\n        >\n          <source src=\"/hero-coming-soon.mp4\" type=\"video/mp4\" />\n          Your browser does not support the video tag.\n        </video>\n        \n        <p className=\"text-white mt-4\">\n          If you see this video playing, the file is working correctly.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,mVAAC;QAAI,WAAU;kBACb,cAAA,mVAAC;YAAI,WAAU;;8BACb,mVAAC;oBAAG,WAAU;8BAA2B;;;;;;8BAGzC,mVAAC;oBACC,QAAQ;oBACR,IAAI;oBACJ,KAAK;oBACL,WAAW;oBACX,QAAQ;oBACR,WAAU;oBACV,cAAc,IAAM,QAAQ,GAAG,CAAC;oBAChC,SAAS,CAAC,IAAM,QAAQ,KAAK,CAAC,gBAAgB;oBAC9C,WAAW,IAAM,QAAQ,GAAG,CAAC;;sCAE7B,mVAAC;4BAAO,KAAI;4BAAwB,MAAK;;;;;;wBAAc;;;;;;;8BAIzD,mVAAC;oBAAE,WAAU;8BAAkB;;;;;;;;;;;;;;;;;AAMvC", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/node_modules/.pnpm/next%4015.3.4_react-dom%4019.1.0_react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,yLAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}