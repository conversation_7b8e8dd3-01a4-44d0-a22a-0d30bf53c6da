{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/i18n/request.ts"], "sourcesContent": ["import {notFound} from 'next/navigation';\nimport {getRequestConfig} from 'next-intl/server';\n\n// Can be imported from a shared config\nconst locales = ['en', 'es', 'fr', 'pl', 'ru'];\n\nexport default getRequestConfig(async ({requestLocale}) => {\n  // This typically corresponds to the `[locale]` segment\n  let locale = await requestLocale;\n\n  // Ensure that a valid locale is used\n  if (!locale || !locales.includes(locale as any)) {\n    locale = 'en';\n  }\n\n  return {\n    locale,\n    messages: (await import(`../messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;AACA;;AAEA,uCAAuC;AACvC,MAAM,UAAU;IAAC;IAAM;IAAM;IAAM;IAAM;CAAK;uCAE/B,CAAA,GAAA,wXAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAC,aAAa,EAAC;IACpD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,CAAC,SAAgB;QAC/C,SAAS;IACX;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;kBAAa,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAChE;AACF", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_c4e516d6.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_c4e516d6-module__qcbB1a__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_c4e516d6.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Sites/ojochal.cr/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import {NextIntlClientProvider} from 'next-intl';\nimport {getMessages} from 'next-intl/server';\nimport {notFound} from 'next/navigation';\nimport {Inter} from 'next/font/google';\nimport type {Metadata} from 'next';\nimport '../globals.css';\n\nconst inter = Inter({\n  subsets: ['latin'],\n  display: 'swap',\n});\n\nconst locales = ['en', 'es', 'fr', 'pl', 'ru'];\n\ntype Props = {\n  children: React.ReactNode;\n  params: Promise<{locale: string}>;\n};\n\nexport async function generateMetadata({params}: Props): Promise<Metadata> {\n  const {locale} = await params;\n\n  // Import messages for SEO\n  const messages = await getMessages({locale});\n  const seoMessages = messages.seo as any;\n\n  // Base URL for canonical URLs\n  const baseUrl = 'https://ojochal.cr';\n\n  // Generate canonical URL\n  const canonicalUrl = locale === 'en' ? baseUrl : `${baseUrl}/${locale}`;\n\n  // Generate alternate URLs for all languages\n  const alternateLanguages: Record<string, string> = {};\n  locales.forEach((loc) => {\n    alternateLanguages[loc] = loc === 'en' ? baseUrl : `${baseUrl}/${loc}`;\n  });\n\n  return {\n    title:\n      seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',\n    description:\n      seoMessages?.description ||\n      'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',\n\n    // Canonical URL\n    alternates: {\n      canonical: canonicalUrl,\n      languages: alternateLanguages,\n    },\n\n    openGraph: {\n      title:\n        seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',\n      description:\n        seoMessages?.description ||\n        'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',\n      url: canonicalUrl,\n      siteName: 'Ojochal.cr',\n      images: [\n        {\n          url: '/og-image.jpg',\n          width: 1200,\n          height: 630,\n          alt: 'Ojochal, Costa Rica - Hidden Gem',\n        },\n      ],\n      locale: locale,\n      type: 'website',\n    },\n\n    twitter: {\n      card: 'summary_large_image',\n      title:\n        seoMessages?.title || 'Discover Ojochal – The Hidden Gem of Costa Rica',\n      description:\n        seoMessages?.description ||\n        'Explore beaches, restaurants, waterfalls, and tours in Ojochal, Puntarenas. Sign up to stay updated.',\n      images: ['/og-image.jpg'],\n    },\n\n    icons: {\n      icon: [\n        {url: '/favicon.svg', type: 'image/svg+xml'},\n        {url: '/favicon.ico', sizes: '32x32'},\n      ],\n    },\n\n    robots: {\n      index: true,\n      follow: true,\n    },\n  };\n}\n\nexport default async function LocaleLayout({children, params}: Props) {\n  const {locale} = await params;\n\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale)) {\n    notFound();\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages({locale});\n\n  return (\n    <html lang={locale} className={inter.className}>\n      <body className=\"antialiased\">\n        <NextIntlClientProvider messages={messages}>\n          {children}\n        </NextIntlClientProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;;;;;;;;AAUA,MAAM,UAAU;IAAC;IAAM;IAAM;IAAM;IAAM;CAAK;AAOvC,eAAe,iBAAiB,EAAC,MAAM,EAAQ;IACpD,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IAEvB,0BAA0B;IAC1B,MAAM,WAAW,MAAM,CAAA,GAAA,8WAAA,CAAA,cAAW,AAAD,EAAE;QAAC;IAAM;IAC1C,MAAM,cAAc,SAAS,GAAG;IAEhC,8BAA8B;IAC9B,MAAM,UAAU;IAEhB,yBAAyB;IACzB,MAAM,eAAe,WAAW,OAAO,UAAU,GAAG,QAAQ,CAAC,EAAE,QAAQ;IAEvE,4CAA4C;IAC5C,MAAM,qBAA6C,CAAC;IACpD,QAAQ,OAAO,CAAC,CAAC;QACf,kBAAkB,CAAC,IAAI,GAAG,QAAQ,OAAO,UAAU,GAAG,QAAQ,CAAC,EAAE,KAAK;IACxE;IAEA,OAAO;QACL,OACE,aAAa,SAAS;QACxB,aACE,aAAa,eACb;QAEF,gBAAgB;QAChB,YAAY;YACV,WAAW;YACX,WAAW;QACb;QAEA,WAAW;YACT,OACE,aAAa,SAAS;YACxB,aACE,aAAa,eACb;YACF,KAAK;YACL,UAAU;YACV,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,QAAQ;YACR,MAAM;QACR;QAEA,SAAS;YACP,MAAM;YACN,OACE,aAAa,SAAS;YACxB,aACE,aAAa,eACb;YACF,QAAQ;gBAAC;aAAgB;QAC3B;QAEA,OAAO;YACL,MAAM;gBACJ;oBAAC,KAAK;oBAAgB,MAAM;gBAAe;gBAC3C;oBAAC,KAAK;oBAAgB,OAAO;gBAAO;aACrC;QACH;QAEA,QAAQ;YACN,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAEe,eAAe,aAAa,EAAC,QAAQ,EAAE,MAAM,EAAQ;IAClE,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM;IAEvB,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAS;QAC7B,CAAA,GAAA,0RAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,8WAAA,CAAA,cAAW,AAAD,EAAE;QAAC;IAAM;IAE1C,qBACE,mVAAC;QAAK,MAAM;QAAQ,WAAW,yIAAA,CAAA,UAAK,CAAC,SAAS;kBAC5C,cAAA,mVAAC;YAAK,WAAU;sBACd,cAAA,mVAAC,gYAAA,CAAA,yBAAsB;gBAAC,UAAU;0BAC/B;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}