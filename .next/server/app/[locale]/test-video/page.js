const CHUNK_PUBLIC_PATH = "server/app/[locale]/test-video/page.js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_efa12a97._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__50a7c09c._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_d3705665._.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_client_components_forbidden-error_0e48df03.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_client_components_unauthorized-error_4e92b3c0.js");
runtime.loadChunk("server/chunks/ssr/messages_dd9ca36e._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_21fa4788._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__65434efd._.js");
runtime.loadChunk("server/chunks/ssr/_5cea75f4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/[locale]/test-video/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/test-video/page { MODULE_0 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/[locale]/test-video/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/test-video/page { MODULE_0 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/app/[locale]/test-video/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
